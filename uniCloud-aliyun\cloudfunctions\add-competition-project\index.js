'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
	console.log('add-competition-project云函数被调用，参数:', event);
	
	try {
		// 直接向xm-stp-project_comp_detail表添加数据
		const result = await db.collection('xm-stp-project_comp_detail').add(event);
		
		console.log('添加成功:', result);
		
		return {
			success: true,
			data: result
		};
	} catch (error) {
		console.error('添加失败:', error);
		
		return {
			success: false,
			error: error.message
		};
	}
};
