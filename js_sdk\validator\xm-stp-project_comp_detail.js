// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "organizers": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      },
      {
        "minLength": 1,
        "maxLength": 500
      }
    ],
    "title": "主办方",
    "label": "主办方"
  },
  "contractors": {
    "rules": [
      {
        "format": "string"
      },
      {
        "minLength": 1,
        "maxLength": 500
      }
    ],
    "title": "承办方",
    "label": "承办方"
  },
  "title": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      },
      {
        "minLength": 1,
        "maxLength": 50
      }
    ],
    "title": "主题",
    "label": "主题"
  },
  "project_detail": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "竞赛名",
    "label": "竞赛名"
  },
  "description": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "详情",
    "label": "详情"
  },
  "avatar": {
    "rules": [
      {
        "format": "file"
      }
    ],
    "label": "封面大图",
    "title": "封面大图"
  },
  "current_team_request_pending": {
    "rules": [
      {
        "format": "int"
      },
      {
        "minimum": 0
      }
    ],
    "title": "现有团队正寻找队伍",
    "label": "现有团队正寻找队伍"
  },
  "team_completed_enroll": {
    "rules": [
      {
        "format": "int"
      },
      {
        "minimum": 0
      }
    ],
    "title": "现有团队已成功报名",
    "label": "现有团队已成功报名"
  }
}

const enumConverter = {}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
