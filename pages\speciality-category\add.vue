<template>
  <view class="uni-container">
    <uni-forms ref="form" :model="formData" validateTrigger="bind">
      <uni-forms-item v-if="notCollegeAdmin" name="college_id" label="学院" required>
        <uni-data-picker v-model="formData.college_id" collection="xm-stp-college_cat" field="_id as value, name as text"></uni-data-picker>
      </uni-forms-item>
      <uni-forms-item name="name" label="科系" required>
        <uni-easyinput placeholder="科系名称" v-model="formData.name"></uni-easyinput>
      </uni-forms-item>
      <view class="uni-button-group">
        <button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
        <navigator open-type="navigateBack" style="margin-left: 15px;">
          <button class="uni-button" style="width: 100px;">返回</button>
        </navigator>
      </view>
    </uni-forms>
  </view>
</template>

<script>
  import { validator } from '../../js_sdk/validator/xm-stp-specific_cat.js';

  const db = uniCloud.database();
  const dbCmd = db.command;
  const dbCollectionName = 'xm-stp-specific_cat';

  function getValidator(fields) {
    let result = {}
    for (let key in validator) {
      if (fields.includes(key)) {
        result[key] = validator[key]
      }
    }
    return result
  }

  

  export default {
    data() {
      let formData = {
        "college_id": "",
        "name": "",
      }
      return {
		notCollegeAdmin:true,
        formData,
        formOptions: {},
        rules: {
          ...getValidator(Object.keys(formData))
        }
      }
    },
    async onReady() {
		const userInfo = uniCloud.getCurrentUserInfo()
		if(userInfo.role.includes('academy admin')){
			this.notCollegeAdmin = false
			

			var userRole = await db.collection('uni-id-users').doc(userInfo.uid).field('academy_role').get()
			console.log(userRole.result.data[0])
			this.formData.college_id = userRole.result.data[0].academy_role
			
		}
      this.$refs.form.setRules(this.rules)
    },
    methods: {
      
      /**
       * 验证表单并提交
       */
      submit() {
        uni.showLoading({
          mask: true
        })
        this.$refs.form.validate().then((res) => {
          return this.submitForm(res)
        }).catch(() => {
        }).finally(() => {
          uni.hideLoading()
        })
      },

      /**
       * 提交表单
       */
      submitForm(value) {
        // 使用 clientDB 提交数据
        return db.collection(dbCollectionName).add(value).then((res) => {
          uni.showToast({
            title: '新增成功'
          })
          this.getOpenerEventChannel().emit('refreshData')
          setTimeout(() => uni.navigateBack(), 500)
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        })
      }
    }
  }
</script>