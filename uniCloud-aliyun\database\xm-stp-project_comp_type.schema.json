// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": false,
		"create": false,
		"update": false,
		"delete": false
	},
	"properties": {
		"_id": {
			"title": "竞赛项目id",
			"description": "竞赛项目ID"
		},
		"title":{
			"bsonType": "string",
			"title": "主题",
			"description": "项目主题",
			"minLength": 1,
			"maxLength": 100
		},
		"description": {
			"bsonType": "string",
			"title": "详情",
			"description": "项目详情"
		},
		"colleges":{
			"bsonType": "array",
			"title": "学院支持",
			"description": "哪些学院能看到",
			"foreignKey": "xm-stp-college_cat._id",
			"enum":{
				"collection": "xm-stp-college_cat",
				"field":"_id as value, name as text"
			}
		},
		"create_time": {
			"bsonType": "timestamp",
			"title": "创建时间",
			"description": "项目的创建时间",
			"forceDefaultValue": {
			  "$env": "now"
			}
		}
	}
}