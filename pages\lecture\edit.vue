<template>
  <view class="uni-container">
    <uni-forms ref="form" :model="formData" validateTrigger="bind">
      <uni-forms-item name="title" label="主题">
        <uni-easyinput placeholder="项目主题" v-model="formData.title"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="description" label="详情">
        <div id="div1">
        	<div v-html="formData.description"></div>
        </div>
      </uni-forms-item>
      <uni-forms-item name="colleges" label="学院支持">
        <uni-data-picker :multiple="true" v-model="formData.colleges" collection="xm-stp-college_cat" field="_id as value, name as text"></uni-data-picker>
      </uni-forms-item>
      <view class="uni-button-group">
        <button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
        <navigator open-type="navigateBack" style="margin-left: 15px;">
          <button class="uni-button" style="width: 100px;">返回</button>
        </navigator>
      </view>
    </uni-forms>
  </view>
</template>

<script>
  import { validator } from '../../js_sdk/validator/xm-stp-lecture.js';
  import E from 'wangeditor'

	let editor = null;

  const db = uniCloud.database();
  const dbCmd = db.command;
  const dbCollectionName = 'xm-stp-lecture';

  function getValidator(fields) {
    let result = {}
    for (let key in validator) {
      if (fields.includes(key)) {
        result[key] = validator[key]
      }
    }
    return result
  }

  

  export default {
    data() {
      let formData = {
        "title": "",
        "description": "",
        "colleges": []
      }
      return {
        formData,
        formOptions: {},
        rules: {
          ...getValidator(Object.keys(formData))
        }
      }
    },
    onLoad(e) {
      if (e.id) {
        const id = e.id
        this.formDataId = id
        this.getDetail(id)
      }
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
	  this.onWangEdit()
    },
    methods: {
      onWangEdit() {
      	editor = new E('#div1')
      	editor.config.zIndex = 0
      	editor.config.onblur = (newHtml) => {
      		this.formData.description = newHtml
      	}
      
      	editor.config.customUploadImg = function(resultFiles, insertImgFn) {
      		resultFiles.forEach(item => {
      			let path = URL.createObjectURL(item)
      			let name = item.name
      			uniCloud.uploadFile({
      				filePath: path,
      				cloudPath: name
      			}).then(res => {
      				insertImgFn(res.fileID)
      			})
      		})
      	}
      
      	editor.create()
      },
      /**
       * 验证表单并提交
       */
      submit() {
        uni.showLoading({
          mask: true
        })
        this.$refs.form.validate().then((res) => {
          return this.submitForm(res)
        }).catch(() => {
        }).finally(() => {
          uni.hideLoading()
        })
      },

      /**
       * 提交表单
       */
      submitForm(value) {
				value.description = editor.txt.html()
        // 使用 clientDB 提交数据
        return db.collection(dbCollectionName).doc(this.formDataId).update(value).then((res) => {
          uni.showToast({
            title: '修改成功'
          })
          this.getOpenerEventChannel().emit('refreshData')
          setTimeout(() => uni.navigateBack(), 500)
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        })
      },

      /**
       * 获取表单数据
       * @param {Object} id
       */
      getDetail(id) {
        uni.showLoading({
          mask: true
        })
        db.collection(dbCollectionName).doc(id).field("title,description,colleges").get().then((res) => {
          const data = res.result.data[0]
          if (data) {
            this.formData = data
            
          }
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        }).finally(() => {
          uni.hideLoading()
        })
      }
    }
  }
</script>
