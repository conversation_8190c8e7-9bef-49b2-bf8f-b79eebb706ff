<template>
  <view class="uni-container">
    <uni-forms ref="form" :model="formData" validateTrigger="bind">
      <uni-forms-item name="user_id" label="用户id" required>
        <uni-data-picker v-model="formData.user_id" collection="xiamen-setup-user_detail" field="_id as value, real_name as text"></uni-data-picker>
      </uni-forms-item>
      <uni-forms-item name="title" label="主题" required>
        <uni-easyinput placeholder="项目主题" v-model="formData.title"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="description" label="详情" required>
        <uni-easyinput placeholder="项目详情" v-model="formData.description"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="person_needed" label="需要人员">
        <uni-easyinput placeholder="项目所需人员" v-model="formData.person_needed"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="current_person_request" label="现有申请人">
        <uni-easyinput placeholder="现有申请人的数量" type="number" v-model="formData.current_person_request"></uni-easyinput>
      </uni-forms-item>
      <view class="uni-button-group">
        <button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
        <navigator open-type="navigateBack" style="margin-left: 15px;">
          <button class="uni-button" style="width: 100px;">返回</button>
        </navigator>
      </view>
    </uni-forms>
  </view>
</template>

<script>
  import { validator } from '../../js_sdk/validator/xm-stp-project_detail.js';

  const db = uniCloud.database();
  const dbCmd = db.command;
  const dbCollectionName = 'xm-stp-project_detail';

  function getValidator(fields) {
    let result = {}
    for (let key in validator) {
      if (fields.includes(key)) {
        result[key] = validator[key]
      }
    }
    return result
  }

  

  export default {
    data() {
      let formData = {
        "user_id": "",
        "title": "",
        "description": "",
        "person_needed": "",
        "current_person_request": null
      }
      return {
        formData,
        formOptions: {},
        rules: {
          ...getValidator(Object.keys(formData))
        }
      }
    },
    onReady() {
      this.$refs.form.setRules(this.rules)
    },
    methods: {
      
      /**
       * 验证表单并提交
       */
      submit() {
        uni.showLoading({
          mask: true
        })
        this.$refs.form.validate().then((res) => {
          return this.submitForm(res)
        }).catch(() => {
        }).finally(() => {
          uni.hideLoading()
        })
      },

      /**
       * 提交表单
       */
      submitForm(value) {
        // 使用 clientDB 提交数据
        return db.collection(dbCollectionName).add(value).then((res) => {
          uni.showToast({
            title: '新增成功'
          })
          this.getOpenerEventChannel().emit('refreshData')
          setTimeout(() => uni.navigateBack(), 500)
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        })
      }
    }
  }
</script>
