{"id": "uni-cloud-router", "name": "uni-cloud-router", "displayName": "uni-cloud-router", "version": "1.0.3", "description": " 基于 koa 风格的 uniCloud 云函数路由库，同时支持 uniCloud 客户端及 URL", "keywords": ["云函数路由", "uniCloud", "uni-cloud-router"], "repository": "https://gitee.com/dcloud/uni-cloud-router", "engines": {"HBuilderX": "^3.1.0"}, "files": ["uniCloud", "changelog.md", "readme.md"], "dcloudext": {"category": ["uniCloud", "云函数模板"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/uni-cloud-router"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "u", "app-nvue": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "u", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}