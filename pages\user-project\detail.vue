<template>
	<view class="container">
	      <!-- 数据展示 -->
	      <view class="container">
	        <view class="info-card">
	          <view class="info-row">
	            <text class="label">名字：</text>
	            <text class="value">{{ data.name }} （{{ options.user_type[data.user_type] }}）</text>
	          </view>
	          <view class="info-row">
	            <text class="label">主题：</text>
	            <text class="value">{{ data.title }} （{{ data.project_type }}）</text>
	          </view>
	          <view class="info-row">
	            <text class="label">需要人数：</text>
	            <text class="value">{{ data.person_needed }}</text>
	          </view>
	          <view class="info-row">
	            <text class="label">现有申请人数：</text>
	            <text class="value">{{ purifyValue(data.current_person_request) }}</text>
	          </view>
			  <view class="info-row">
			    <text class="label">项目创建时间：</text>
			    <text class="value">{{formatTimestamp(data.create_time) }}</text>
			  </view>
	          <view class="info-row">
	            <text class="label">结束时间：</text>
	            <text class="value">{{formatTimestampInSeconds(data.ending_time) }}</text>
	          </view>
	        </view>
	      </view>
		  <div class="content" >
			  详情：
			  <div class="info-card" v-html="data.description"></div>
		  </div>
	  </view>
</template>

<script>
import { enumConverter } from '../../js_sdk/validator/xm-stp-project_detail.js';
const db = uniCloud.database();

export default {
  data() {
    return {
		data: {},
     //  queryWhere: '',
     //  collectionList: [
     //    db.collection('xm-stp-project_detail')
     //      .field('user_id,title,description,person_needed,current_person_request,user_type')
     //      .getTemp(),
     //    db.collection('xm-stp-project')
     //      .field('_id,type_id,ending_time')
     //      .getTemp(),
     //    db.collection('xm-stp-user_detail')
     //      .field('_id, real_name as text')
     //      .getTemp(),
     //  ],
     //  loadMore: {
     //    contentdown: '加载更多...',
     //    contentrefresh: '加载中...',
     //    contentnomore: '没有更多内容了',
     //  },
      options: {
        user_type: ['老师', '本科生', '硕士（研究生）', '博士（研究生）'],
     } ,
    };
  },
  onLoad(e) {
    this._id = e.id;
	
  },
  async onReady() {
	  
    if (this._id) {
		const data = {
			'id':this._id
		}
		const res = await uniCloud.callFunction({
			name:'back_project_get',
			data:data
		})
		
		console.log(res.result.status)
		if(res.result.status == 0) {
			uni.showToast({
				title:res.result.msg,
				icon:'error'
			})
		}
		
		this.data = res.result.data
		console.log(res.result)
      // this.collectionList = [
      //   db.collection('xm-stp-project_detail')
      //     .where(`_id=="${this._id}"`)
      //     .field('user_id,title,description,person_needed,current_person_request,user_type')
      //     .getTemp(),
      //   db.collection('xm-stp-user_detail')
      //     .field('_id, real_name as text')
      //     .getTemp(),
      // ];
    }
  },
  methods: {
    purifyValue(value) {
      if (value == undefined || value == null || value == '') return 0;
      return value;
    },
	formatTimestamp(timestamp) {
	    const date = new Date(timestamp); // 创建一个 Date 对象
	    const year = date.getFullYear();
	    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从 0 开始的
	    const day = String(date.getDate()).padStart(2, '0');
	    const hours = String(date.getHours()).padStart(2, '0');
	    const minutes = String(date.getMinutes()).padStart(2, '0');
	
	    return `${year}-${month}-${day} ${hours}:${minutes}`;
	},
	formatTimestampInSeconds(timestampInSeconds) {
	    const date = new Date(timestampInSeconds * 1000); // 转换为毫秒
	    const year = date.getFullYear();
	    const month = String(date.getMonth() + 1).padStart(2, '0');
	    const day = String(date.getDate()).padStart(2, '0');
	    const hours = String(date.getHours()).padStart(2, '0');
	    const minutes = String(date.getMinutes()).padStart(2, '0');
	
	    return `${year}-${month}-${day} ${hours}:${minutes}`;
	}
  },
};
</script>

<style>
.container {
  padding: 10px;
  background-color: #f8f8f8;
}

.content{
	padding:10px;
  background-color: #f8f8f8;
  height: auto;
}

.info-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info-row {
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
  color: #333;
}

.value {
  color: #555;
}

.error-message {
  color: red;
  text-align: center;
  margin-top: 20px;
}

.loading {
  text-align: center;
  margin-top: 20px;
}
</style>
