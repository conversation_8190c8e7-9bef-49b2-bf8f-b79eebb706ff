<template>
	<view class="uni-container">
		<uni-forms ref="form" :model="formData" validateTrigger="bind">
			<uni-forms-item name="username" label="用户名(测试阶段)">
				<uni-easyinput placeholder="用户名" v-model="formData.username"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="password" label="密码(测试阶段)">
				<uni-easyinput placeholder="密码" v-model="formData.password"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="real_name" label="真实名字" required>
				<uni-easyinput placeholder="用户的真实名字" v-model="formData.real_name"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="onboarding_year" label="入职/入学年份" required>
				<uni-easyinput placeholder="用户的入职或入学年份" type="number"
					v-model="formData.onboarding_year"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="type" label="用户类型" required>
				<uni-data-checkbox v-model="formData.type" :localdata="formOptions.type_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="college_category_id" label="学院id" required>
				<uni-data-picker v-model="formData.college_category_id" collection="xm-stp-college_cat"
					field="_id as value, name as text"></uni-data-picker>
			</uni-forms-item>
			<uni-forms-item v-show="formData.type" name="specific_category_id" label="科系id" required>
				<!-- <uni-data-picker v-model="formData.specific_category_id" collection="xm-stp-specific_cat" field="_id as value, name as text, college_id as isleaf"></uni-data-picker> -->
				<uni-data-picker v-model="formData.specific_category_id" :localdata="specificOption"></uni-data-picker>
			</uni-forms-item>
			
			<uni-forms-item name="school_id" label="学校id" required>
				<uni-easyinput placeholder="学生为学号，老师为导师号" type="number" v-model="formData.school_id"></uni-easyinput>
			</uni-forms-item>
			<view class="uni-button-group">
				<button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
				<navigator open-type="navigateBack" style="margin-left: 15px;">
					<button class="uni-button" style="width: 100px;">返回</button>
				</navigator>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import {
		validator
	} from '../../js_sdk/validator/xm-stp-user_detail.js';

	const db = uniCloud.database();
	const dbCmd = db.command;
	const dbCollectionName = 'xm-stp-user_detail';

	function getValidator(fields) {
		let result = {}
		for (let key in validator) {
			if (fields.includes(key)) {
				result[key] = validator[key]
			}
		}
		return result
	}



	export default {
		data() {
			let formData = {
				"username": "",
				"password": "",
				"real_name": "",
				"onboarding_year": 2000,
				"college_category_id": "",
				"specific_category_id": "",
				"type": null,
				"school_id": null,
			}
			return {
				formData,
				formOptions: {
					"type_localdata": [{
						"value": 0,
						"text": "老师"
					}, {
						"value": 1,
						"text": "本科生"
					}, {
						"value": 2,
						"text": "硕士（研究生）"
					}, {
						"value": 3,
						"text": "博士（研究生）"
					}],
				},
				specificList: [],
				rules: {
					...getValidator(Object.keys(formData))
				}
			}
		},
		async onReady() {
			this.$refs.form.setRules(this.rules)
			const res = await db.collection("xm-stp-specific_cat").get()

			this.specificList = res.result.data
		},
		computed: {
			specificOption() {
				if (this.formData.college_category_id == "") return []

				const list = []
				for (const index in this.specificList)
					if (this.specificList[index].college_id == this.formData.college_category_id)
						list.push({
							value: this.specificList[index]._id,
							text: this.specificList[index].name
						})

				return list
			}
		},
		methods: {
			/**
			 * 验证表单并提交
			 */
			submit() {
				uni.showLoading({
					mask: true
				})
				this.$refs.form.validate().then((res) => {
					return this.submitForm(res)
				}).catch(() => {}).finally(() => {
					uni.hideLoading()
				})
			},

			/**
			 * 提交表单
			 */
			async submitForm(value) {
				const res = await uniCloud.importObject('Sign').register(value)
				if(!res.status){
					uni.showModal({
						content: res.msg || '请求服务失败',
						showCancel: false
					})
					return 
				}
				delete value['username']
				delete value['password']
				
				value._id = res.data.id
				// 使用 clientDB 提交数据
				return db.collection(dbCollectionName).add(value).then((res) => {
					console.log(res)
					uni.showToast({
						title: '新增成功'
					})
					this.getOpenerEventChannel().emit('refreshData')
					setTimeout(() => uni.navigateBack(), 500)
				}).catch((err) => {
					console.log(err)
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				})
			}
		}
	}
</script>