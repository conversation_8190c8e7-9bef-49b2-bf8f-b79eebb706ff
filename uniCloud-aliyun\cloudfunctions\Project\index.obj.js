// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
const db = uniCloud.databaseForJQL()
const dbCmd = db.command

const {
	convertStatus,
	convertPosition,
	getDetailForUpdate
} = require('b_project')
const {
	formatDateTime
} = require('date')

module.exports = {
	_before: function() { // 通用预处理器
		db.setUser({ // 指定后续执行操作的用户信息，此虚拟用户将同时拥有传入的uid、role、permission
			role: ['admin'], // 指定当前执行用户的角色为admin。如果只希望指定为admin身份，可以删除uid和permission字段
		})
	},
	async getListByCat(data) {
		const cat = await db.collection('xm-stp-project_cat').where(`name=='${data.name}'`).get()
		
		if (cat.affectedDocs == 0) return {
			status: 0,
			msg: "不合格类别"
		}

		const detail = await db.collection('xm-stp-project_detail').field('_id,title,person_needed')
			.getTemp()

		var condition = data.name == '竞赛项目' ? 
			`type_id == '${cat.data[0]._id}' && status == 1 && competition_id != NULL` 
			:
			`type_id == '${cat.data[0]._id}' && status == 1`;
		
		if(data.user_id != null) condition += ` && user_id != '${data.user_id}'`
		
		let res = await db.collection('xm-stp-project', detail)
			.where(condition)
			.field('_id,user_id,competition_id,create_time,ending_time')
			.get()
		
		
		const projIds = []
		for(const i in res.data){
			res.data[i].title = res.data[i]._id['xm-stp-project_detail'][0].title
			res.data[i].person_needed = res.data[i]._id['xm-stp-project_detail'][0].person_needed
			res.data[i]._id = res.data[i]._id._value
			projIds.push(res.data[i]._id)
		}
		
		if(data.user_id){
			const inProj = await db.collection('xm-stp-project_app_request')
				.where({
					project_id:dbCmd.in(projIds),
					user_id:data.user_id
				})
				.get()
			
			if(inProj.affectedDocs){
				for(const i1 in res.data){
					for(const i2 in inProj.data){
						if(res.data[i1]._id == inProj.data[i2].project_id){
							res.data[i1].in_project = 1
							break
						}
					}
				}
			}
		}
		
		
		if(data.name == '竞赛项目'){
			const compIds = []
			for(const i in res.data){
				compIds.push(res.data[i].competition_id)
			}
			
			const comp = await db.collection('xm-stp-project_comp_detail')
			.where({
				_id:dbCmd.in(compIds),
			})
			.field('title')
			.get()
			
			for(const i1 in res.data){
				for(const i2 in comp.data){
					if(res.data[i1].competition_id == comp.data[i2]._id){
						res.data[i1].comp_name = comp.data[i2].title
						delete res.data[i1].competition_id
					}
				}
			}
		}
		
		return {
			status: 1,
			msg: "OK",
			data: res.data
		}
	},
	
	async getListForMainPage(data){
		// 这个操作是确保不会获取到竞赛项目
		const comp = await db.collection('xm-stp-project_cat').where({name:'竞赛项目'}).get()
		// 竞赛项目
		var condition = 'status == 1'
		if(comp.affectedDocs) condition += ` && type_id != '${comp.data[0]._id}'`
		// 如果有用户id就不拿他的
		if(data.user_id) condition += ` && user_id != '${data.user_id}'`
		
		// 获取随机5个
		const list = await db.collection('xm-stp-project').where(condition)
			.field('type_id,create_time,ending_time').orderBy('create_time','desc').limit(5).get()
		if(list.affectedDocs == 0)  return {
			status:1,
			msg:"OK",
			data: []
		}
		
		const projs = []
		var cats = []
		for(const i in list.data)
		{
			projs.push(list.data[i]._id)
			cats.push(list.data[i].type_id)
		}
		cats = Array.from(new Set(cats))
		
		// 获取对应的项目详情
		const detail = await db.collection('xm-stp-project_detail').where({
			_id:dbCmd.in(projs)
		}).field('title,person_needed').get()
		
		for(const i1 in list.data){
			for(const i2 in detail.data){
				if(list.data[i1]._id == detail.data[i2]._id){
					list.data[i1].title = detail.data[i2].title
					list.data[i1].person_needed = detail.data[i2].person_needed
					break
				}
			}
		}
		
		// 获取类别数据
		const cat = await db.collection('xm-stp-project_cat').where({
			_id:dbCmd.in(cats)
		}).get()
		
		for(const i1 in list.data){
			for(const i2 in cat.data){
				if(list.data[i1].type_id == cat.data[i2]._id){
					list.data[i1].type = cat.data[i2].name
					delete list.data[i1].type_id
					break
				}
			}
		}
		
		return {
			status:1,
			msg:"OK",
			data: list.data
		}
	},
	async getDetailFromList(data){
		const res = await db.collection('xm-stp-project_detail').doc(data.id).field('description,user_id').get()
		if(res.affectedDocs == 0) return {
			status:0,
			msg: "不存在该项目"
		}
		
		const $ = db.command.aggregate
		const counter = await db.collection('xm-stp-project_app_request').aggregate()
		    .match({
		        project_id: data.id 
		    })
		    .group({
		        _id: '$project_id', // 按 project_id 分组
		        count: $.sum(1) // 统计每组的数量
		    })
		    .end();
		
		if(!counter.affectedDocs) res.data[0].person_pending = 0
		else res.data[0].person_pending = counter.data[0].count
		
		return {
			status:1,
			msg:"OK",
			data:res.data[0]
		}
	},
	async getSelf(data) {
		let check = await db.collection('xm-stp-project_detail')
			.where(`user_id=='${data.user_id}'`)
			.orderBy('create_time desc')
			.field('title,person_needed,create_time')
			.get()

		if (check.affectedDocs == 0) return {
			status: 1,
			msg: "OK",
			data: []
		}

		const listId = []
		for (const i in check.data)
			listId.push(check.data[i]._id)

		const detail = check.data

		const res = await db.collection('xm-stp-project').where({
			_id: dbCmd.in(listId)
		}).get()

		const project = res.data

		const list = []
		const prjTypeList = []
		const compIds = []
		var index = 0
		for (const i1 in detail) {
			for (const i2 in project) {
				if (detail[i1]._id == project[i2]._id) {
					list.push({
						...detail[i1],
						...project[i2]
					})
					if(list[index].competition_id) compIds.push(list[index].competition_id)					
					prjTypeList.push(list[index++].type_id)
					break
				}
			}
		}
		
		if(compIds.length){
			const compList = await db.collection('xm-stp-project_comp_detail')
			.where({
				_id: dbCmd.in([...new Set(compIds)])
			})
			.field('title').get()
			
			for(const i1 in list){
				for(const i2 in compList.data){
					if(list[i1].competition_id == compList.data[i2]._id){
						list[i1].comp = compList.data[i2].title
						delete list[i1].competition_id
						break
					}
				}
			}
		}

		const projectTypeDb = await db.collection('xm-stp-project_cat').where({
			_id: dbCmd.in([...new Set(prjTypeList)])
		}).get()

		projectTypes = {}
		for (const i in projectTypeDb.data) {
			projectTypes[projectTypeDb.data[i]._id] = projectTypeDb.data[i].name
		}
		
		const projIds = []
		for (const i in list) {
			projIds.push(list[i]._id)
			list[i].type = projectTypes[list[i].type_id]
			delete list[i].type_id
			list[i].status = convertStatus(list[i].status)
		}
		
		const $ = db.command.aggregate
		const counter = await db.collection('xm-stp-project_app_request').aggregate()
		    .match({
		        project_id: dbCmd.in(projIds) // 条件过滤：project_id 在指定列表中
		    })
		    .group({
		        _id: '$project_id', // 按 project_id 分组
		        count: $.sum(1) // 统计每组的数量
		    })
		    .end();

		for(const i1 in counter.data){
			for(const i2 in list){
				if(list[i2]._id == counter.data[i1]._id){
					list[i2].person_request = counter.data[i1].count
					break
				}
			}
		}
		
		
		
		return {
			status: 1,
			msg: "OK",
			data: list
		}

	},
	async getDetail(data) {
		// 获取项目详情
		const check = await db.collection('xm-stp-project_detail')
			.doc(data.id).get()

		if (check.affectedDocs == 0) return {
			status: 0,
			msg: "项目不存在"
		}

		// 获取项目的时间和类型
		const project = await db.collection('xm-stp-project,xm-stp-project_cat')
			.where({
				'_id': data.id
			})
			.get()

		project.data[0].type = project.data[0].type_id[0].name
		delete project.data[0].type_id
		
		// 如果是隶属于竞赛
		if(project.data[0].competition_id){
			const comp = await db.collection('xm-stp-project_comp_detail')
			.doc(project.data[0].competition_id).field('title').get()
			
			if(comp.affectedDocs) project.data[0].comp = comp.data[0].title
		}
		
		// 获取项目允许学院信息
		const academyNeeded = await db.collection('xm-stp-project_cat_relation, xm-stp-college_cat')
			.where({
				'project_id': data.id
			})
			.foreignKey("xm-stp-project_cat_relation.college_category_id")
			.get()
		
		const academyList = []
		for (const i in academyNeeded.data)
			academyList.push(academyNeeded.data[i].college_category_id[0])
		
		// 获取项目中用户的信息
		const userDetailTmp = await db.collection('xm-stp-user_detail').field('_id,real_name').getTemp()
		const memberInProject = await db.collection('xm-stp-project_detail_user_rel', userDetailTmp)
			.where({
				project_id: data.id,
				project_position: dbCmd.lte(2)
			})
			.orderBy('project_position asc')
			.get()

		let haveUser = 0
		
		for (const i in memberInProject.data) {
			if(data.user_id && data.user_id == memberInProject.data[i].user_id[0]) haveUser = 1 
			memberInProject.data[i].user = memberInProject.data[i].user_id[0]
			memberInProject.data[i].project_position = convertPosition(memberInProject.data[i].project_position)
			delete memberInProject.data[i].user_id
		}
		
		// 先提前把数据整理
		const result = {
				...project.data[0],
				...check.data[0],
				...{
					'academyList': academyList
				},
				...{
					'memberList': memberInProject.data
				}
			}

		// 如果有用户信息但是并不存在于项目里【可能用户申请了加入但还没被处理】
		if(data.user_id && !haveUser){
			const checkExist = await db.collection('xm-stp-project_app_request').where({
				'user_id':data.user_id,
				'project_id':data.id
			}).field('_id').get()
			
			// 如果发现存在用户那么设置pending
			if(checkExist.affectedDocs){
				result.pending = 1
			}
		}

		return {
			status: 1,
			msg: 'OK',
			data: result
		}

	},

	async addProject(data) {
		let check = await db.collection('xm-stp-project_cat').doc(data.type_id).get()
		if (check.affectedDocs == 0) return {
			status: 0,
			msg: "项目类型不存在"
		}

		check = await db.collection('xm-stp-college_cat').where({
			_id: dbCmd.in(data.college_categories)
		}).get()

		if (check.affectedDocs != data.college_categories.length)
			return {
				status: 0,
				msg: "有学院是不存在"
			}
		const transaction = await db.startTransaction()
		// try{
		const res = await db.collection('xm-stp-project').add({
			type_id: data.type_id,
			ending_time: data.ending_time,
			status: parseInt(data.status),
			user_id: data.user_id
		})

		const projectId = res.id

		await db.collection('xm-stp-project_detail').add({
			_id: projectId,
			user_id: data.user_id,
			title: data.title,
			description: data.description,
			person_needed: parseInt(data.person_needed),
			user_type: data.user_type
		})

		await db.collection('xm-stp-project_detail_user_rel').add({
			user_id: data.user_id,
			project_id: projectId,
			project_position: parseInt(convertPosition("项目负责人"))
		})

		const catList = []
		for (const index in data.college_categories)
			catList.push({
				project_id: projectId,
				college_category_id: data.college_categories[index]
			})


		await db.collection('xm-stp-project_cat_relation').add(catList)

		await transaction.commit()
		// }catch(\throw e){
		// 	await transaction.rollback()

		// 	return {
		// 		status:0,
		// 		msg:"项目添加失败，请再重试或者上报给技术部门"
		// 	}
		// }


		return {
			status: 1,
			msg: "项目添加成功"
		}
	},
	async getDetailForUpdate(data) {
		return await getDetailForUpdate(data)
	},
	async updateProject(data) {
		// 获取旧数据, 并对数据进行整理
		const res = await getDetailForUpdate(data)
		if (res.status == 0) return res
		delete res.data._id
		res.data.ending_time = formatDateTime(res.data.ending_time, 'YYYY-mm-dd')

		// 检查数据是否真的有更新，没更新则不做操作并返回通知
		const updatedData = {}
		for (const key in res.data) {
			// 如果数据是为array或者object另外进行分析
			if (typeof data[key] == 'object' || typeof data[key] == 'array') {
				if (data[key].length != res.data[key].length) {
					updatedData[key] = data[key]
					continue
				}
				for (const k1 in data[key]) {
					if (data[key][k1] != res.data[key][k1]) {
						updatedData[key] = data[key]
						continue
					}
				}
				continue
			}

			if (data[key] != res.data[key]) {
				updatedData[key] = data[key]
			}
		}

		if (Object.keys(updatedData).length == 0) return {
			status: 0,
			msg: '参数没变过，不需要更新'
		}


		// 逐个更新
		const transaction = await db.startTransaction()
		// 如果存在project的字段
		if ('ending_time' in updatedData || 'status' in updatedData) {
			const projData = {}
			if ('ending_time' in updatedData)
				projData['ending_time'] = Math.floor(new Date(updatedData['ending_time']).getTime() / 1000)

			if ('status' in updatedData)
				projData['status'] = parseInt(updatedData['status'])

			await db.collection('xm-stp-project').doc(data.id).update(projData)
			delete updatedData['ending_time']
			delete updatedData['status']
		}

		// 如果存在学院的字段
		if ('academyList' in updatedData) {
			await db.collection('xm-stp-project_cat_relation').where({
				project_id: data.id
			}).remove()

			const list = []
			for (const i in updatedData['academyList'])
				list.push({
					project_id: data.id,
					college_category_id: updatedData['academyList'][i]
				})

			await db.collection('xm-stp-project_cat_relation').add(list)
			delete updatedData['academyList']
		}

		// 上述的处理后如果还有键值，那么就是detail更新用
		if (Object.keys(updatedData).length > 0) {
			await db.collection('xm-stp-project_detail').doc(data.id).update(updatedData)
		}


		await transaction.commit()

		return {
			status: 1,
			msg: '更新成功'
		}

	}

}