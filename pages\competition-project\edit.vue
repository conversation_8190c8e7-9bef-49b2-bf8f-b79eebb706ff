<template>
	<view class="uni-container">
		<uni-forms ref="form" :model="formData" validateTrigger="bind">
			<uni-forms-item name="title" label="主题" required>
				<uni-easyinput placeholder="项目主题" v-model="formData.title"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="project_detail" label="竞赛名" required>
				<uni-data-picker v-model="formData.project_detail" collection="xm-stp-project_comp_type" field="_id as value, title as text"></uni-data-picker>
			</uni-forms-item>
			<uni-forms-item name="organizers" label="主办方" required>
				<uni-easyinput placeholder="主办方（可以以，分割）" v-model="formData.organizers"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="contractors" label="承办方">
				<uni-easyinput placeholder="承办方（可以以，分割）" v-model="formData.contractors"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="avatar" label="封面大图">
				<uni-file-picker return-type="object" v-model="formData.avatar"></uni-file-picker>
			</uni-forms-item>
			<uni-forms-item name="status" label="项目状态">
				<uni-data-checkbox v-model="formData.status" :localdata="formOptions.status_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="ending_time" label="已结束时间" required>
				<uni-datetime-picker return-type="timestamp" v-model="formData.ending_time"></uni-datetime-picker>
			</uni-forms-item>
			<uni-forms-item name="description" label="详情" required>
				<div id="div1">
					<div v-html="formData.description"></div>
				</div>
				<!-- <uni-easyinput placeholder="项目详情" v-model="formData.description"></uni-easyinput> -->
			</uni-forms-item>

			<view class="uni-button-group">
				<button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
				<navigator open-type="navigateBack" style="margin-left: 15px;">
					<button class="uni-button" style="width: 100px;">返回</button>
				</navigator>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import {
		validator
	} from '../../js_sdk/validator/xm-stp-project_comp_detail.js';
	import E from 'wangeditor'

	let editor = null;

	const db = uniCloud.database();
	const dbCmd = db.command;
	const dbCollectionName = 'xm-stp-project_comp_detail';

	function getValidator(fields) {
		let result = {}
		for (let key in validator) {
			if (fields.includes(key)) {
				result[key] = validator[key]
			}
		}
		return result
	}



	export default {
		data() {
			let formData = {
				"organizers": "",
				"contractors": "",
				"status": null,
				"title": "",
				"project_detail": "",
				"description": "",
				"avatar": null
			}
			return {
				defaultFormData: {},
				formData,
				formOptions: {
				  "status_localdata": [
					{
					  "value": 0,
					  "text": "草稿箱"
					},
					{
					  "value": 1,
					  "text": "正常"
					},
					{
					  "value": 2,
					  "text": "已废弃"
					}
				  ]
				},
				rules: {
					...getValidator(Object.keys(formData))
				}
			}
		},
		onLoad(e) {
			if (e.id) {
				const id = e.id
				this.formDataId = id
				this.getDetail(id)
			}
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
			this.onWangEdit()
		},
		methods: {
			onWangEdit() {
				editor = new E('#div1')
				editor.config.zIndex = 0
				editor.config.onblur = (newHtml) => {
					this.formData.description = newHtml
				}

				editor.config.customUploadImg = function(resultFiles, insertImgFn) {
					resultFiles.forEach(item => {
						let path = URL.createObjectURL(item)
						let name = item.name
						uniCloud.uploadFile({
							filePath: path,
							cloudPath: name
						}).then(res => {
							insertImgFn(res.fileID)
						})
					})
				}

				editor.create()
			},
			/**
			 * 验证表单并提交
			 */
			async submit() {
				uni.showLoading({
					mask: true
				})
				this.$refs.form.validate().then(async res => {
					return await this.submitForm(res)
				}).catch(() => {}).finally(() => {
					uni.hideLoading()
				})
			},

			/**
			 * 提交表单
			 */
			async submitForm(value) {
				value.description = editor.txt.html()
				const list = [];
				for (const key in this.defaultFormData)
					if (this.defaultFormData[key] != this.formData[key]) list.push(key)

				if (list.indexOf('ending_time') !== -1 || list.indexOf('status') !== -1 ) {
					await db.collection('xm-stp-project').doc(this.formDataId).update({
						'ending_time': value['ending_time'],
						'status':value['status']
					})
				}
				delete value['ending_time']
				delete value['status']

				// 使用 clientDB 提交数据
				return db.collection(dbCollectionName).doc(this.formDataId).update(value).then((res) => {
					uni.showToast({
						title: '修改成功'
					})
					this.getOpenerEventChannel().emit('refreshData')
					setTimeout(() => uni.navigateBack(), 500)
				}).catch((err) => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				})
			},

			/**
			 * 获取表单数据
			 * @param {Object} id
			 */
			async getDetail(id) {
				uni.showLoading({
					mask: true
				})


				let res = await db.collection(dbCollectionName).doc(id).field(
					"organizers,contractors,project_detail,title,description,ending_time").get()

				const data = res.result.data[0]
				if (data) {
					this.formData = data
				}

				res = await db.collection('xm-stp-project').doc(id).field('status,ending_time').get()
				console.log(res)
				this.formData.ending_time = res.result.data[0].ending_time
				this.formData.status = res.result.data[0].status

				this.defaultFormData = {
					...this.formData
				}
				uni.hideLoading()

			}
		}
	}
</script>