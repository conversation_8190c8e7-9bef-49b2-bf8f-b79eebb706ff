{"passwordSecret": "passwordSecret-demo", "tokenSecret": "tokenSecret-demo", "tokenExpiresIn": 7200, "tokenExpiresThreshold": 3600, "passwordErrorLimit": 6, "bindTokenToDevice": false, "passwordErrorRetryTime": 3600, "autoSetInviteCode": false, "forceInviteCode": false, "app": {"tokenExpiresIn": 2592000, "tokenExpiresThreshold": 864000, "oauth": {"weixin": {"appid": "填写来源微信开放平台https://open.weixin.qq.com/创建的应用的appid", "appsecret": "填写来源微信开放平台https://open.weixin.qq.com/创建的应用的appsecret"}, "apple": {"bundleId": "苹果开发者后台获取的bundleId"}}}, "web": {"tokenExpiresIn": 7200, "tokenExpiresThreshold": 3600, "oauth": {"weixin-h5": {"appid": "微信公众号登录所用的appid、appsecret需要在对应的小程序管理控制台获取", "appsecret": "微信公众号后台获取的appsecret"}, "weixin-web": {"appid": "微信PC页面扫码登录配置appid", "appsecret": "微信PC页面扫码登录配置appsecret"}}}, "mp-weixin": {"tokenExpiresIn": 259200, "tokenExpiresThreshold": 86400, "oauth": {"weixin": {"appid": "微信小程序登录所用的appid、appsecret需要在对应的小程序管理控制台获取", "appsecret": "微信小程序后台获取的appsecret"}}}, "mp-alipay": {"tokenExpiresIn": 259200, "tokenExpiresThreshold": 86400, "oauth": {"alipay": {"appid": "支付宝小程序登录用到的appid、privateKey请参考支付宝小程序的文档进行设置或者获取，https://opendocs.alipay.com/open/291/105971#LDsXr", "privateKey": "支付宝小程序登录用到的appid、privateKey请参考支付宝小程序的文档进行设置或者获取，https://opendocs.alipay.com/open/291/105971#LDsXr"}}}, "service": {"sms": {"name": "应用名称，对应短信模版的name", "codeExpiresIn": 300, "smsKey": "短信密钥key，开通短信服务处可以看到", "smsSecret": "短信密钥secret，开通短信服务处可以看到", "scene": {"bind-mobile-by-sms": {"templateId": "绑定手机号使用的短信验证码模板", "codeExpiresIn": 240}}}, "univerify": {"appid": "当前应用的appid，使用云函数URL化，此项必须配置", "apiKey": "apiKey 和 apiSecret 在开发者中心获取，开发者中心：https://dev.dcloud.net.cn/uniLogin/index?type=0，文档：https://ask.dcloud.net.cn/article/37965", "apiSecret": ""}}}