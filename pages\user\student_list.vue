<template>
	<view>
		<view class="uni-header">
			<view class="uni-group">
				<view class="uni-title"></view>
				<view class="uni-sub-title"></view>
			</view>
			<view class="uni-group">
				<input class="uni-search" type="text" v-model="query" @confirm="search" placeholder="请输入搜索内容" />
				<button class="uni-button" type="default" size="mini" @click="search">搜索</button>
				<!-- <button class="uni-button" type="default" size="mini" @click="navigateTo('./add')">新增</button> -->
				<button class="uni-button" type="default" size="mini" :disabled="!selectedIndexs.length"
					@click="delTable">批量删除</button>
				<download-excel class="hide-on-phone" :fields="exportExcel.fields" :data="exportExcelData"
					:type="exportExcel.type" :name="exportExcel.filename">
					<button class="uni-button" type="primary" size="mini">导出 Excel</button>
				</download-excel>
			</view>
		</view>
		<view class="uni-container">
			<unicloud-db ref="udb" :collection="collectionList" :where="where" page-data="replace" :orderby="orderby"
				:getcount="true" :page-size="options.pageSize" :page-current="options.pageCurrent"
				v-slot:default="{data,pagination,loading,error,options}" :options="options" loadtime="manual"
				@load="onqueryload">
				<uni-table ref="table" :loading="loading" :emptyText="error.message || '没有更多数据'" border stripe
					type="selection" @selection-change="selectionChange">
					<uni-tr>
						<uni-th align="center" filter-type="search" @filter-change="filterChange($event, 'real_name')"
							sortable @sort-change="sortChange($event, 'real_name')">真实名字</uni-th>
						<uni-th align="center" filter-type="range" @filter-change="filterChange($event, 'school_id')"
							sortable @sort-change="sortChange($event, 'school_id')">学校id</uni-th>
						<uni-th align="center" filter-type="select" :filter-data="options.filterData.type_localdata"
							@filter-change="filterChange($event, 'type')">用户类型</uni-th>
						<uni-th align="center" filter-type="range"
							@filter-change="filterChange($event, 'onboarding_year')" sortable
							@sort-change="sortChange($event, 'onboarding_year')">入职/入学年份</uni-th>
						<uni-th align="center">学院id</uni-th>
						<uni-th align="center">科系id</uni-th>
						<!-- <uni-th align="center">操作</uni-th> -->
					</uni-tr>
					<uni-tr v-for="(item,index) in data" :key="index">
						<uni-td align="center">{{item.real_name}}</uni-td>
						<uni-td align="center">{{item.school_id}}</uni-td>
						<uni-td align="center">{{options.type_valuetotext[item.type]}}</uni-td>
						<uni-td align="center">{{item.onboarding_year}}</uni-td>
						<uni-td
							align="center">{{item.college_category_id && item.college_category_id[0] && item.college_category_id[0].text}}</uni-td>
						<uni-td
							align="center">{{item.specific_category_id && item.specific_category_id[0] && item.specific_category_id[0].text}}</uni-td>
						<!-- <uni-td align="center">
							<view class="uni-group">
								<button @click="navigateTo('./detail?id='+item._id, false)" class="uni-button" size="mini"
									type="info">查看详情</button>
								<button @click="navigateTo('./edit?id='+item._id, false)" class="uni-button" size="mini"
									type="primary">修改</button>
								<button @click="confirmDelete(item._id)" class="uni-button" size="mini"
									type="warn">删除</button>
							</view>
						</uni-td> -->
					</uni-tr>
				</uni-table>
				<view class="uni-pagination-box">
					<uni-pagination show-icon :page-size="pagination.size" v-model="pagination.current"
						:total="pagination.count" @change="onPageChanged" />
				</view>
			</unicloud-db>
		</view>
	</view>
</template>

<script>
	import {
		enumConverter,
		filterToWhere
	} from '../../js_sdk/validator/xm-stp-user_detail.js';

	const db = uniCloud.database()
	// 表查询配置
	const dbOrderBy = '' // 排序字段
	const dbSearchFields = ['real_name',
		'college_category_id.text'] // 模糊搜索字段，支持模糊搜索的字段列表。联表查询格式: 主表字段名.副表字段名，例如用户表关联角色表 role.role_name
	// 分页配置
	const pageSize = 20
	const pageCurrent = 1

	const orderByMapping = {
		"ascending": "asc",
		"descending": "desc"
	}

	export default {
		data() {
			return {
				collectionList: [db.collection('xm-stp-user_detail').where('type != 0').field(
						'real_name,school_id,type,onboarding_year,college_category_id,specific_category_id')
					.getTemp(), db.collection('xm-stp-college_cat').field('_id, name as text').getTemp(),
					db.collection('xm-stp-specific_cat').field('_id, name as text').getTemp()
				],
				query: '',
				where: '',
				orderby: dbOrderBy,
				orderByFieldName: "",
				selectedIndexs: [],
				options: {
					pageSize,
					pageCurrent,
					filterData: {
						"type_localdata": [{
							"value": 0,
							"text": "老师"
						}, {
							"value": 1,
							"text": "本科生"
						}, {
							"value": 2,
							"text": "硕士（研究生）"
						}, {
							"value": 3,
							"text": "博士（研究生）"
						}]
					},
					...enumConverter
				},
				imageStyles: {
					width: 64,
					height: 64
				},
				exportExcel: {
					"filename": "xm-stp-user_detail.xls",
					"type": "xls",
					"fields": {
						"真实名字": "real_name",
						"学校id": "school_id",
						"用户类型": "type",
						"入职/入学年份": "onboarding_year",
						"学院id": "college_category_id",
						"科系id": "specific_category_id"
					}
				},
				exportExcelData: []
			}
		},
		onLoad() {
			this._filter = {}
		},
		onReady() {
			this.$refs.udb.loadData()
		},
		methods: {
			onqueryload(data) {
				this.exportExcelData = data
			},
			getWhere() {
				const query = this.query.trim()
				if (!query) {
					return ''
				}
				const queryRe = new RegExp(query, 'i')
				return dbSearchFields.map(name => queryRe + '.test(' + name + ')').join(' || ')
			},
			search() {
				const newWhere = this.getWhere()
				console.log(newWhere)
				this.where = newWhere
				this.$nextTick(() => {
					this.loadData()
				})
			},
			loadData(clear = true) {
				this.$refs.udb.loadData({
					clear
				})
			},
			onPageChanged(e) {
				this.selectedIndexs.length = 0
				this.$refs.table.clearSelection()
				this.$refs.udb.loadData({
					current: e.current
				})
			},
			navigateTo(url, clear) {
				// clear 表示刷新列表时是否清除页码，true 表示刷新并回到列表第 1 页，默认为 true
				uni.navigateTo({
					url,
					events: {
						refreshData: () => {
							this.loadData(clear)
						}
					}
				})
			},
			// 多选处理
			selectedItems() {
				var dataList = this.$refs.udb.dataList
				return this.selectedIndexs.map(i => dataList[i]._id)
			},
			// 批量删除
			delTable() {
				this.$refs.udb.remove(this.selectedItems(), {
					success: (res) => {
						this.$refs.table.clearSelection()
					}
				})
			},
			// 多选
			selectionChange(e) {
				this.selectedIndexs = e.detail.index
			},
			confirmDelete(id) {
				this.$refs.udb.remove(id, {
					success: (res) => {
						this.$refs.table.clearSelection()
					}
				})
			},
			sortChange(e, name) {
				this.orderByFieldName = name;
				if (e.order) {
					this.orderby = name + ' ' + orderByMapping[e.order]
				} else {
					this.orderby = ''
				}
				this.$refs.table.clearSelection()
				this.$nextTick(() => {
					this.$refs.udb.loadData()
				})
			},
			filterChange(e, name) {
				this._filter[name] = {
					type: e.filterType,
					value: e.filter
				}
				let newWhere = filterToWhere(this._filter, db.command)
				if (Object.keys(newWhere).length) {
					this.where = newWhere
				} else {
					this.where = ''
				}
				this.$nextTick(() => {
					this.$refs.udb.loadData()
				})
			}
		}
	}
</script>

<style>
</style>