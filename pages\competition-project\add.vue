<template>
	<view class="uni-container">
		<uni-forms ref="form" :model="formData" validateTrigger="bind">
			<uni-forms-item name="title" label="主题" required>
				<uni-easyinput placeholder="项目主题" v-model="formData.title"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="project_detail" label="竞赛名" required>
				<uni-data-picker v-model="formData.project_detail" collection="xm-stp-project_comp_type" field="_id as value, title as text"></uni-data-picker>
			</uni-forms-item>
			<uni-forms-item name="organizers" label="主办方" required>
				<uni-easyinput placeholder="主办方（可以以，分割）" v-model="formData.organizers"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="contractors" label="承办方">
				<uni-easyinput placeholder="承办方（可以以，分割）" v-model="formData.contractors"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="avatar" label="封面大图">
				<uni-file-picker return-type="object" v-model="formData.avatar"></uni-file-picker>
			</uni-forms-item>
			<uni-forms-item name="status" label="项目状态">
				<uni-data-checkbox v-model="formData.status" :localdata="formOptions.status_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="ending_time" label="已结束时间" required>
				<uni-datetime-picker type="datetime" return-type="timestamp" :clear-icon="false" :start="getCurrentDateFunction()" v-model="formData.ending_time"></uni-datetime-picker>
			</uni-forms-item>
			<uni-forms-item name="description" label="详情" required>
				<div id="div1">

				</div>
				<!-- <uni-easyinput placeholder="项目详情" v-model="formData.description"></uni-easyinput> -->
			</uni-forms-item>
			<view class="uni-button-group">
				<button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
				<navigator open-type="navigateBack" style="margin-left: 15px;">
					<button class="uni-button" style="width: 100px;">返回</button>
				</navigator>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import {
		validator
	} from '../../js_sdk/validator/xm-stp-project_comp_detail.js';
	import E from 'wangeditor'

	let editor = null;

	const db = uniCloud.database();
	const dbCmd = db.command;
	const dbCollectionName = 'xm-stp-project_comp_detail';

	function getValidator(fields) {
		let result = {}
		for (let key in validator) {
			if (fields.includes(key)) {
				result[key] = validator[key]
			}
		}
		return result
	}



	export default {
		data() {
			let formData = {
				"organizers": "",
				"contractors": "",
				"status": 0,
				"title": "",
				"project_detail": "",
				"description": "",
				"ending_time": null,
				"avatar": null
			}
			return {
				type_id: '',
				formData,
				formOptions: {
				  "status_localdata": [
					{
					  "value": 0,
					  "text": "草稿箱"
					},
					{
					  "value": 1,
					  "text": "正常"
					},
					{
					  "value": 2,
					  "text": "已废弃"
					}
				  ]
				},
				rules: {
					...getValidator(Object.keys(formData))
				}
			}
		},
		mounted() {
			console.log('mounted生命周期被调用')
			// 在mounted中也尝试初始化编辑器
			setTimeout(() => {
				console.log('mounted中的setTimeout执行')
				this.initEditor()
			}, 1000)
		},
		async onReady() {
			console.log('onReady被调用')
			this.$refs.form.setRules(this.rules)

			// 异步获取竞赛项目类型ID
			try {
				// 先查看所有的项目类型
				const allTypes = await db.collection('xm-stp-project_cat').get()
				console.log('所有项目类型:', allTypes.result.data)

				const type = await db.collection('xm-stp-project_cat').where({
					'name': '竞赛项目'
				}).limit(1).get()
				if (type.result && type.result.data && type.result.data.length > 0) {
					this.type_id = type.result.data[0]._id
					console.log('获取到type_id:', this.type_id)
				} else {
					console.error('未找到竞赛项目类型')
					// 尝试获取第一个可用的类型作为默认值
					if (allTypes.result && allTypes.result.data && allTypes.result.data.length > 0) {
						this.type_id = allTypes.result.data[0]._id
						console.log('使用默认type_id:', this.type_id)
					} else {
						uni.showToast({
							title: '未找到任何项目类型，请联系管理员',
							icon: 'none'
						})
					}
				}
			} catch (error) {
				console.error('获取type_id失败:', error)
				uni.showToast({
					title: '获取项目类型失败',
					icon: 'none'
				})
			}

			console.log('准备初始化编辑器')
			// 延迟初始化编辑器，确保DOM完全渲染
			this.$nextTick(() => {
				console.log('$nextTick执行')
				setTimeout(() => {
					console.log('setTimeout执行，准备调用onWangEdit')
					try {
						console.log('直接在这里初始化编辑器')
						if (typeof E !== 'undefined') {
							console.log('E (wangEditor) 已加载')
							const editorElement = document.getElementById('div1')
							console.log('div1元素:', editorElement)
							if (editorElement) {
								editor = new E('#div1')
								editor.config.zIndex = 0
								editor.config.onblur = (newHtml) => {
									this.formData.description = newHtml
								}
								editor.create()
								console.log('编辑器创建成功')
							} else {
								console.error('找不到div1元素')
							}
						} else {
							console.error('wangEditor (E) 未加载')
						}
					} catch (error) {
						console.error('编辑器初始化失败:', error)
					}
				}, 500)
			})
		},
		methods: {
			initEditor() {
				console.log('initEditor方法被调用')
				try {
					if (typeof E !== 'undefined') {
						console.log('E (wangEditor) 已加载')
						const editorElement = document.getElementById('div1')
						console.log('div1元素:', editorElement)
						if (editorElement) {
							// 如果编辑器已存在，先销毁
							if (window.editor) {
								try {
									window.editor.destroy()
								} catch (e) {
									console.log('销毁旧编辑器时出错:', e)
								}
							}

							window.editor = new E('#div1')
							window.editor.config.zIndex = 0
							window.editor.config.onblur = (newHtml) => {
								this.formData.description = newHtml
							}
							window.editor.create()
							console.log('编辑器创建成功')
						} else {
							console.error('找不到div1元素')
						}
					} else {
						console.error('wangEditor (E) 未加载')
					}
				} catch (error) {
					console.error('编辑器初始化失败:', error)
				}
			},
			onWangEdit() {
				console.log('开始初始化wangEditor')
				try {
					console.log('检查div1元素:', document.getElementById('div1'))
					editor = new E('#div1')
					console.log('创建编辑器实例成功')

					editor.config.zIndex = 0
					editor.config.onblur = (newHtml) => {
						this.formData.description = newHtml
					}

					editor.config.customUploadImg = function(resultFiles, insertImgFn) {
						resultFiles.forEach(item => {
							let path = URL.createObjectURL(item)
							let name = item.name
							uniCloud.uploadFile({
								filePath: path,
								cloudPath: name
							}).then(res => {
								insertImgFn(res.fileID)
							})
						})
					}

					editor.create()
					console.log('编辑器创建完成')
				} catch (error) {
					console.error('编辑器初始化失败:', error)
				}
			},
			getCurrentDateFunction() {
				const today = new Date();
			
				// 获取年份
				const year = today.getFullYear();
			
				// 获取月份（注意：月份从0开始，所以需要加1）
				const month = String(today.getMonth() + 1).padStart(2, '0');
			
				// 获取日期
				const day = String(today.getDate()).padStart(2, '0');
			
				// 组合成 YYYY-MM-DD 格式
				return `${year}-${month}-${day}`;
			},
			/**
			 * 验证表单并提交
			 */
			submit() {
				uni.showLoading({
					mask: true
				})
				this.$refs.form.validate().then((res) => {
					return this.submitForm(res)
				}).catch(() => {}).finally(() => {
					uni.hideLoading()
				})
			},

			/**
			 * 提交表单
			 */
			async submitForm(value) {
				return db.collection('xm-stp-project').add({
					ending_time: value.ending_time,
					type_id: this.type_id,
					status: value.status
				}).then(res => {
					value._id = res.result.id
					delete value['ending_time']
					delete value['status']
					// 使用 clientDB 提交数据
					db.collection(dbCollectionName).add(value).then((res) => {
						uni.showToast({
							title: '新增成功'
						})
						this.getOpenerEventChannel().emit('refreshData')
						setTimeout(() => uni.navigateBack(), 500)
					}).catch((err) => {
						uni.showModal({
							content: err.message || '请求服务失败',
							showCancel: false
						})
					})
				}).catch((err) => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				})

			}
		}
	}
</script>

<style>
#div1 {
	min-height: 200px;
	border: 1px solid #ccc;
	border-radius: 4px;
}

/* wangEditor 样式修复 */
.w-e-text-container {
	min-height: 200px !important;
}

.w-e-text {
	min-height: 200px !important;
}
</style>