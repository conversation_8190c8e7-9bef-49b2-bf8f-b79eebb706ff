// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
const db = uniCloud.databaseForJQL()
const dbCmd = db.command
const { getStatus } = require('project-history')

module.exports = {
	_before: function () { // 通用预处理器
		db.setUser({
			role: ['admin'], 
		})
	},
	// 申请加入项目（用户申请加入项目）
	async requestJoin(data){
		const check = await db.collection('xm-stp-project_app_request')
		.where({
			user_id:data.user_id,
			project_id:data.project_id,
		})
		.get()
		
		if(check.affectedDocs == 1) return {
			status:0,
			msg:"你已经申请过该项目"
		}
		const check2 = await db.collection('xm-stp-project_app_invite')
		.where({
			user_id:data.user_id,
			project_id:data.project_id,
		})
		.get()
		if(check2.affectedDocs == 1) return {
			status:0,
			msg:"你已被邀请于这个项目"
		}
		
		const transaction = await db.startTransaction()
		await db.collection('xm-stp-project_app_request').add({
			user_id:data.user_id,
			project_id:data.project_id,
			comment:data.introduce,
			status:0
		})
		
		await db.collection('xm-stp-project_app_history').add({
			user_id:data.user_id,
			project_id:data.project_id,
			action:parseInt(getStatus('申请加入'))
		})
		// todo 可以考虑发消息给用户
		
		await transaction.commit()
		
		return {
			status:1,
			msg:"申请成功"
		}
	},
	// 邀请加入项目（项目拥有者邀请别人）
	async inviteJoin(data){
		
	},
	async getInvitedList(data){
		
	},
	async getJoinList(data){
		const res = await db.collection('xm-stp-project_app_request')
		.where({'user_id':data.user_id})
		.field('project_id,status,create_time')
		.get()
		
		if(res.affectedDocs == 0) return {
			status:1,
			msg:"OK",
			data:[]
		}
		
		const projIds = []
		for(const i in res.data)
			projIds.push(res.data[i].project_id)
		
		const projectDetail = await db.collection('xm-stp-project_detail')
			.where({
				'_id':dbCmd.in(projIds)
			})
			.field('title,person_needed')
			.get()
			
			
		for(const i1 in res.data){
			for(const i2 in projectDetail.data){
				if(res.data[i1].project_id == projectDetail.data[i2]._id){
					res.data[i1].title = projectDetail.data[i2].title
					res.data[i1].person_needed = projectDetail.data[i2].person_needed
					res.data[i1].status = getStatus(res.data[i1].status)
					break
				}
			}
		}
		
		const project = await db.collection('xm-stp-project')
			.where({
				'_id':dbCmd.in(projIds)
			})
			.field('type_id,competition_id,ending_time')
			.get()
		
		const prjTypeList = []
		const compIds = []
		for(const i in project.data){
			prjTypeList.push(project.data[i].type_id)
			
			if(project.data[i].competition_id) 
				compIds.push(project.data[i].competition_id)
		}
		
		const projectTypeDb = await db.collection('xm-stp-project_cat').where({
			_id: dbCmd.in([...new Set(prjTypeList)])
		}).get()
		
		for(const i1 in project.data){
			for(const i2 in projectTypeDb.data){
				if(project.data[i1].type_id == projectTypeDb.data[i2]._id){
					project.data[i1].type = projectTypeDb.data[i2].name
					delete project.data[i1].type_id
					break
				}
			}
		}
		
		if(compIds.length){
			const compList = await db.collection('xm-stp-project_comp_detail')
			.where({
				_id: dbCmd.in([...new Set(compIds)])
			})
			.field('title').get()
			
			for(const i1 in project.data){
				for(const i2 in compList.data){
					if(project.data[i1].competition_id == compList.data[i2]._id){
						project.data[i1].comp = compList.data[i2].title
						delete project.data[i1].competition_id
						break
					}
				}
			}
		}
		
		for(const i1 in res.data){
			for(const i2 in project.data){
				if(res.data[i1].project_id == project.data[i2]._id){
					res.data[i1].ending_time = project.data[i2].ending_time
					res.data[i1].type = project.data[i2].type
					
					if(project.data[i2].comp) res.data[i1].comp = project.data[i2].comp
					break
				}
			}
		}
		
		const $ = db.command.aggregate
		const counter = await db.collection('xm-stp-project_app_request').aggregate()
		    .match({
		        project_id: dbCmd.in(projIds) // 条件过滤：project_id 在指定列表中
		    })
		    .group({
		        _id: '$project_id', // 按 project_id 分组
		        count: $.sum(1) // 统计每组的数量
		    })
		    .end();
		
				console.log(counter)
		for(const i1 in counter.data){
			for(const i2 in res.data){
				if(res.data[i2].project_id == counter.data[i1]._id){
					res.data[i2].person_request = counter.data[i1].count
					break
				}
			}
		}
		
		return {
			status:1,
			msg:"OK",
			data:res.data
		}
	}
}
