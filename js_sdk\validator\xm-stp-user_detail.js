// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "real_name": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      },
      {
        "minLength": 2,
        "maxLength": 100
      }
    ],
    "title": "真实名字",
    "label": "真实名字"
  },
  "school_id": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "int"
      }
    ],
    "title": "学校id",
    "label": "学校id"
  },
  "type": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "int"
      },
      {
        "range": [
          {
            "value": 0,
            "text": "老师"
          },
          {
            "value": 1,
            "text": "本科生"
          },
          {
            "value": 2,
            "text": "硕士（研究生）"
          },
          {
            "value": 3,
            "text": "博士（研究生）"
          }
        ]
      }
    ],
    "title": "用户类型",
    "label": "用户类型"
  },
  "onboarding_year": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "int"
      }
    ],
    "title": "入职/入学年份",
    "label": "入职/入学年份"
  },
  "college_category_id": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "学院id",
    "label": "学院id"
  },
  "specific_category_id": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "科系id",
    "label": "科系id"
  }
}

const enumConverter = {
  "type_valuetotext": {
    "0": "老师",
    "1": "本科生",
    "2": "硕士（研究生）",
    "3": "博士（研究生）"
  }
}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
