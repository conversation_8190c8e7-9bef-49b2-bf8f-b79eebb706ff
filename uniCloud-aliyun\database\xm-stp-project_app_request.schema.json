{"bsonType": "object", "required": ["user_id", "project_id", "comment"], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "user_id": {"bsonType": "string", "title": "用户ID", "description": "用户ID", "foreignKey": "xm-stp-user._id"}, "project_id": {"bsonType": "string", "title": "项目ID", "description": "项目ID", "foreignKey": "xm-stp-project_detail._id"}, "comment": {"bsonType": "string", "title": "备注", "description": "备注", "maxLength": 500}, "status": {"bsonType": "int", "title": "状态", "description": "状态【0:等待回复，1已接受】"}, "create_time": {"bsonType": "timestamp", "title": "创建时间", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}}}