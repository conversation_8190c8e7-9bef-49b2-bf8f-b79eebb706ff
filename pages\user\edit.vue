<template>
	<view class="uni-container">
		<uni-forms ref="form" :model="formData" validateTrigger="bind">
			<uni-forms-item name="real_name" label="真实名字" required>
				<uni-easyinput placeholder="用户的真实名字" v-model="formData.real_name"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="school_id" label="学校id" required>
				<uni-easyinput placeholder="学生为学号，老师为导师号" type="number" v-model="formData.school_id"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="type" label="用户类型" required>
				<uni-data-checkbox v-model="formData.type" :localdata="formOptions.type_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="onboarding_year" label="入职/入学年份" required>
				<uni-easyinput placeholder="用户的入职或入学年份" type="number"
					v-model="formData.onboarding_year"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="college_category_id" label="学院id">
				<uni-data-picker v-model="formData.college_category_id" collection="xm-stp-college_cat"
					field="_id as value, name as text"></uni-data-picker>
			</uni-forms-item>
			<uni-forms-item v-if="formData.type" name="specific_category_id" label="科系id">
				<uni-data-picker v-model="formData.specific_category_id" collection="xm-stp-specific_cat"
					field="_id as value, name as text"></uni-data-picker>
			</uni-forms-item>
			<view class="uni-button-group">
				<button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
				<navigator open-type="navigateBack" style="margin-left: 15px;">
					<button class="uni-button" style="width: 100px;">返回</button>
				</navigator>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import {
		validator
	} from '../../js_sdk/validator/xm-stp-user_detail.js';

	const db = uniCloud.database();
	const dbCmd = db.command;
	const dbCollectionName = 'xm-stp-user_detail';

	function getValidator(fields) {
		let result = {}
		for (let key in validator) {
			if (fields.includes(key)) {
				result[key] = validator[key]
			}
		}
		return result
	}



	export default {
		data() {
			let formData = {
				"real_name": "",
				"school_id": null,
				"type": null,
				"onboarding_year": 1900,
				"college_category_id": "",
				"specific_category_id": ""
			}
			return {
				formData,
				formOptions: {
					"type_localdata": [{
						"value": 0,
						"text": "老师"
					}, {
						"value": 1,
						"text": "本科生"
					}, {
						"value": 2,
						"text": "硕士（研究生）"
					}, {
						"value": 3,
						"text": "博士（研究生）"
					}]
				},
				rules: {
					...getValidator(Object.keys(formData))
				}
			}
		},
		onLoad(e) {
			if (e.id) {
				const id = e.id
				this.formDataId = id
				this.getDetail(id)
			}
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
		},
		methods: {

			/**
			 * 验证表单并提交
			 */
			submit() {
				uni.showLoading({
					mask: true
				})
				this.$refs.form.validate().then((res) => {
					return this.submitForm(res)
				}).catch(() => {}).finally(() => {
					uni.hideLoading()
				})
			},

			/**
			 * 提交表单
			 */
			submitForm(value) {
				// 使用 clientDB 提交数据
				return db.collection(dbCollectionName).doc(this.formDataId).update(value).then((res) => {
					uni.showToast({
						title: '修改成功'
					})
					this.getOpenerEventChannel().emit('refreshData')
					setTimeout(() => uni.navigateBack(), 500)
				}).catch((err) => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				})
			},

			/**
			 * 获取表单数据
			 * @param {Object} id
			 */
			getDetail(id) {
				uni.showLoading({
					mask: true
				})
				db.collection(dbCollectionName).doc(id).field(
					"real_name,school_id,type,onboarding_year,college_category_id,specific_category_id").get().then((
					res) => {
					const data = res.result.data[0]
					if (data) {
						this.formData = data

					}
				}).catch((err) => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				}).finally(() => {
					uni.hideLoading()
				})
			}
		}
	}
</script>