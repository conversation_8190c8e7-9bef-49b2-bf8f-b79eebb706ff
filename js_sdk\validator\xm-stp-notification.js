// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "title": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "主题",
    "label": "主题"
  },
  "description": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "详情",
    "label": "详情"
  },
  "status": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "int"
      },
      {
        "range": [
          {
            "value": 0,
            "text": "草稿箱"
          },
          {
            "value": 1,
            "text": "正常"
          },
          {
            "value": 2,
            "text": "已废弃"
          }
        ]
      }
    ],
    "title": "状态",
    "defaultValue": 0,
    "label": "状态"
  },
  "type": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "int"
      },
      {
        "range": [
          {
            "value": 0,
            "text": "所有人"
          },
          {
            "value": 1,
            "text": "学院"
          },
          {
            "value": 2,
            "text": "项目"
          },
          {
            "value": 3,
            "text": "导师"
          },
          {
            "value": 4,
            "text": "本科生"
          },
          {
            "value": 5,
            "text": "硕士"
          },
          {
            "value": 6,
            "text": "博士"
          }
        ]
      }
    ],
    "title": "类型",
    "label": "类型"
  },
  "type_id": {
    "rules": [
      {
        "format": "array"
      }
    ],
    "title": "类型的id",
    "label": "类型的id"
  }
}

const enumConverter = {
  "status_valuetotext": {
    "0": "草稿箱",
    "1": "正常",
    "2": "已废弃"
  },
  "type_valuetotext": {
    "0": "所有人",
    "1": "学院",
    "2": "项目",
    "3": "导师",
    "4": "本科生",
    "5": "硕士",
    "6": "博士"
  }
}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
