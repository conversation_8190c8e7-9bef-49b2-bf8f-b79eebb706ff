<template>
  <view class="uni-container">
    <uni-forms ref="form" :model="formData" validateTrigger="bind">
      <uni-forms-item name="title" label="主题" required>
        <uni-easyinput v-model="formData.title"></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="description" label="详情" required>
      		  <div id="div1">
      		  			  
      		  </div>
        <!-- <uni-easyinput placeholder="项目详情" v-model="formData.description"></uni-easyinput> -->
      </uni-forms-item>
      <uni-forms-item name="status" label="状态" required>
        <uni-data-checkbox v-model="formData.status" :localdata="formOptions.status_localdata"></uni-data-checkbox>
      </uni-forms-item>
      <uni-forms-item name="type" label="类型" required>
        <uni-data-checkbox v-model="formData.type" :localdata="formOptions.type_localdata"></uni-data-checkbox>
      </uni-forms-item>
	  <uni-forms-item v-show="[1,3,4,5,6].includes(formData.type)"  name="type_picked" label="是否全学院">
		  <switch :checked="formData.type_all == 1" @change="collegeChange('checked', $event.detail.value)" /> 
		  <span style="color:red;">*</span><span style="font-size:14px; color:#606266">只是用来方便设置学院, 具体参照学院支持</span>
	  </uni-forms-item>
	  <uni-forms-item v-show="[1,3,4,5,6].includes(formData.type)" name="type_id" label="学院支持" required>
	  		<uni-data-checkbox style="padding-top: 10rpx;" :multiple="true" v-model="formData.type_id" 
			collection="xm-stp-college_cat" field="_id as value, name as text"></uni-data-checkbox>
	  </uni-forms-item>
      <view class="uni-button-group">
        <button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
        <navigator open-type="navigateBack" style="margin-left: 15px;">
          <button class="uni-button" style="width: 100px;">返回</button>
        </navigator>
      </view>
    </uni-forms>
  </view>
</template>

<script>
  import { validator } from '../../js_sdk/validator/xm-stp-notification.js';
  import E from 'wangeditor'

  let editor = null;

  const db = uniCloud.database();
  const dbCmd = db.command;
  const dbCollectionName = 'xm-stp-notification';

  function getValidator(fields) {
    let result = {}
    for (let key in validator) {
      if (fields.includes(key)) {
        result[key] = validator[key]
      }
    }
    return result
  }

  

  export default {
    data() {
      let formData = {
        "title": "",
        "description": "",
        "status": 0,
        "type": null,
		"type_all":0,
        "type_id": []
      }
      return {
        formData,
        formOptions: {
          "status_localdata": [
            {
              "value": 0,
              "text": "草稿箱"
            },
            {
              "value": 1,
              "text": "正常"
            },
            {
              "value": 2,
              "text": "已废弃"
            }
          ],
          "type_localdata": [
            {
              "value": 0,
              "text": "所有人"
            },
            {
              "value": 1,
              "text": "学院"
            },
            {
              "value": 3,
              "text": "导师"
            },
            {
              "value": 4,
              "text": "本科生"
            },
            {
              "value": 5,
              "text": "硕士"
            },
            {
              "value": 6,
              "text": "博士"
            }
          ]
        },
        rules: {
          ...getValidator(Object.keys(formData))
        },
		collegeList:[]
      }
    },
    async onReady() {
      this.$refs.form.setRules(this.rules)
	  this.onWangEdit()
	  
	  const col = await db.collection('xm-stp-college_cat').field('_id').get()
	  
	  for(const i in col.result.data){
		  console.log(col.result.data[i])
		  this.collegeList.push(col.result.data[i]._id)  
	  }
	},
    methods: {
      collegeChange(name, value) {
			this.formData.type_all = value
			this.$refs.form.setValue(name, value)
			
			if(value) this.formData.type_id = Array.from(this.collegeList)
			else this.formData.type_id = []
		},

      /**
       * 验证表单并提交
       */
      submit() {
        uni.showLoading({
          mask: true
        })
        this.$refs.form.validate().then((res) => {
          return this.submitForm(res)
        }).catch(() => {
        }).finally(() => {
          uni.hideLoading()
        })
      },
		onWangEdit(){
      		  editor = new E('#div1')
      		  editor.config.zIndex = 0
      		  editor.config.onblur = (newHtml) =>{
      			  this.formData.description = newHtml
      		  }
      		  
      		  editor.config.customUploadImg = function (resultFiles, insertImgFn) {
      			  resultFiles.forEach(item=>{
      				  let path = URL.createObjectURL(item)
      				  let name = item.name
      				  uniCloud.uploadFile({
      				  	filePath:path,
      					cloudPath:name
      				  }).then(res =>{
      					  insertImgFn(res.fileID)
      				  })
      			  })
      		  }
      		  
      		  editor.create()
       },
      /**
       * 提交表单
       */
      submitForm(value) {
		  value.description = editor.txt.html()
		  
		  // console.log(value)
		  // return 
        // 使用 clientDB 提交数据
        return db.collection(dbCollectionName).add(value).then((res) => {
          uni.showToast({
            title: '新增成功'
          })
          this.getOpenerEventChannel().emit('refreshData')
          setTimeout(() => uni.navigateBack(), 500)
        }).catch((err) => {
          uni.showModal({
            content: err.message || '请求服务失败',
            showCancel: false
          })
        })
      }
    }
  }
</script>