<template>
  <view class="news-container">
    <unicloud-db ref="udb" v-slot:default="{ data, loading, error, options }" :options="options" :collection="collectionList" :getone="true" :manual="true">
      <view v-if="error" class="error">{{ error.message }}</view>
      <view v-else-if="loading" class="loading">
        <uni-load-more :contentText="loadMore" status="loading"></uni-load-more>
      </view>
      <view v-else-if="data" class="news-content">
        <!-- 标题和基本信息 -->
        <div class="header">
          <h1>{{ data.title }}</h1>
          <div class="meta-info">
            <span>移除时间: 
              <uni-dateformat :threshold="[0, 0]" :date="data.remove_time"></uni-dateformat>
            </span>
            <span v-if="data.operated_by"> | 操作员ID: {{ data.operated_by[0]?.text }}</span>
          </div>
        </div>

        <!-- 封面图片 -->
        <div v-if="data.avatar" class="cover-image">
          <uni-file-picker v-if="data.avatar.fileType == 'image'" :value="data.avatar" readonly></uni-file-picker>
          <uni-link v-else :href="data.avatar.url">{{ data.avatar.url }}</uni-link>
        </div>

        <!-- 文章内容 -->
        <div class="article-content">
          <h2>文章内容
            <button class="copy-button" @click="copyContent">复制</button>
          </h2>
          <div ref="paragraphContent" v-html="data.paragraph"></div>
        </div>

      </view>
    </unicloud-db>
  </view>
</template>

<script>
  import { enumConverter } from '../../js_sdk/validator/xm-stp-news.js'
  const db = uniCloud.database()

  export default {
    data() {
      return {
        collectionList: [
          db.collection('xm-stp-news').field('title,avatar,paragraph,operated_by,remove_time').getTemp(),
          db.collection('uni-id-users').field('_id, nickname as text').getTemp()
        ],
        loadMore: {
          contentdown: '',
          contentrefresh: '',
          contentnomore: ''
        },
        options: { ...enumConverter }
      }
    },
    onLoad(e) {
      this._id = e.id
    },
    onReady() {
      if (this._id) {
        this.collectionList = [
          db.collection('xm-stp-news').where('_id == "' + this._id + '"').field('title,avatar,paragraph,status,operated_by,remove_time').getTemp(),
          db.collection('uni-id-users').field('_id, nickname as text').getTemp()
        ]
      }
    },
    methods: {
      copyContent() {
        const paragraphElement = this.$refs.paragraphContent;
        if (paragraphElement) {
          const contentToCopy = paragraphElement.innerHTML;
          navigator.clipboard.writeText(contentToCopy)
            .then(() => {
              alert("内容已复制！");
            })
            .catch((err) => {
              console.error("复制失败:", err);
            });
        }
      }
    }
  }
</script>

<style>
  .news-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
  }

  .header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ddd;
  }

  .header h1 {
    font-size: 2em;
    margin-bottom: 5px;
    color: #333;
  }

  .meta-info {
    font-size: 0.9em;
    color: #888;
  }

  .cover-image {
    margin: 20px 0;
  }

  .article-content h2 {	
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }

  .copy-button {
    margin-left: 10px;
    background-color: transparent;
    cursor: pointer;
  }

  .copy-button:hover {
    background-color: #007BFF;
    color: #fff;
  }

  .article-content p {
    line-height: 1.6;
    font-size: 1.1em;
    color: #555;
  }

  .error, .loading {
    text-align: center;
    color: #999;
  }
</style>
